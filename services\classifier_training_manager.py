#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分类器训练管理器

负责管理多分类器投票系统的训练，包括：
- FBCSP + SVM分类器
- TEF + 随机森林分类器  
- Riemannian + MDM分类器
- 投票机制和性能评估
"""

import os
import pickle
import logging
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from sklearn.svm import SVC
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.metrics import accuracy_score, classification_report
import time

logger = logging.getLogger(__name__)

@dataclass
class ClassifierPerformance:
    """分类器性能信息"""
    accuracy: float
    cv_scores: List[float]
    cv_mean: float
    cv_std: float
    training_time: float
    n_samples: int

# 原有的简单投票系统已被加权投票系统替代
# 相关代码已移至 services/weighted_voting_classifier.py

class ClassifierTrainingManager:
    """分类器训练管理器"""
    
    def __init__(self, config_path: str = None, classifier_config_path: str = None):
        """
        初始化分类器训练管理器

        Args:
            config_path: 特征提取配置文件路径，如果为None则使用默认配置
            classifier_config_path: 分类器配置文件路径，如果为None则使用默认配置
        """
        self.logger = logging.getLogger(__name__)

        # 使用路径管理器获取模型目录
        from utils.path_manager import get_data_file, path_manager
        self.models_dir = str(get_data_file("models"))
        path_manager.ensure_directory_exists(self.models_dir)

        # 加载特征提取配置
        if config_path:
            from services.feature_extraction.config import load_config
            self.config = load_config(config_path)
        else:
            from services.feature_extraction.config import load_config
            self.config = load_config()

        # 加载分类器配置
        from utils.path_manager import get_config_file_in_dir

        if classifier_config_path:
            self.classifier_config = self._load_raw_config(classifier_config_path)
        elif config_path and isinstance(config_path, str) and 'optimized' in config_path:
            # 如果使用优化的特征配置，也使用优化的分类器配置
            optimized_path = str(get_config_file_in_dir('classifiers_optimized.json'))
            self.classifier_config = self._load_raw_config(optimized_path)
        else:
            # 默认使用优化的分类器配置
            optimized_path = str(get_config_file_in_dir('classifiers_optimized.json'))
            self.classifier_config = self._load_raw_config(optimized_path)

        # 从配置文件加载分类器配置
        self.classifier_configs = self._load_classifier_configs_from_config()

        self.logger.info(f"分类器训练管理器初始化完成，支持的算法: {list(self.classifier_configs.keys())}")
        self.logger.info(f"✅ 使用特征配置: {config_path or 'default(optimized)'}")
        self.logger.info(f"✅ 使用分类器配置: {classifier_config_path or 'default(optimized)'}")

    def _load_raw_config(self, config_path):
        """加载原始JSON配置文件"""
        import json
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            self.logger.info(f"成功加载配置文件: {config_path}")
            return config
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            # 返回默认配置
            from services.feature_extraction.config import load_config
            return load_config()

    def _load_classifier_configs_from_config(self):
        """从配置文件加载分类器配置"""
        classifier_configs = {}

        # 获取分类器配置
        if hasattr(self.classifier_config, 'get'):
            # 字典格式
            classifiers_config = self.classifier_config
        elif self.classifier_config:
            # 其他格式
            classifiers_config = self.classifier_config
        else:
            # 没有分类器配置，使用默认方法
            self.logger.info("未找到分类器配置，使用默认配置")
            return self._load_default_classifier_configs()

        # 定义特征类型映射
        feature_type_mapping = {
            'fbcsp_svm': 'fbcsp',
            'tef_rf': 'tef',
            'riemannian_meanfield': 'riemannian',
            'tangent_space_lr': 'tangent_space',
            'plv_svm': 'plv'
        }

        for classifier_name, feature_type in feature_type_mapping.items():
            if classifier_name in classifiers_config:
                config = classifiers_config[classifier_name]
                classifier = self._create_classifier_from_config(config)
                classifier_configs[classifier_name] = {
                    'classifier': classifier,
                    'feature_type': feature_type
                }
                self.logger.info(f"从配置文件加载分类器: {classifier_name}")
            else:
                # 如果配置文件中没有，使用默认方法创建
                self.logger.warning(f"配置文件中未找到 {classifier_name}，使用默认配置")
                classifier_configs[classifier_name] = {
                    'classifier': self._create_default_classifier(classifier_name),
                    'feature_type': feature_type
                }

        return classifier_configs

    def _load_default_classifier_configs(self):
        """加载默认分类器配置（向后兼容）"""
        feature_type_mapping = {
            'fbcsp_svm': 'fbcsp',
            'tef_rf': 'tef',
            'riemannian_meanfield': 'riemannian',
            'tangent_space_lr': 'tangent_space',
            'plv_svm': 'plv'
        }

        classifier_configs = {}
        for classifier_name, feature_type in feature_type_mapping.items():
            classifier_configs[classifier_name] = {
                'classifier': self._create_default_classifier(classifier_name),
                'feature_type': feature_type
            }

        return classifier_configs

    def _create_classifier_from_config(self, config):
        """根据配置创建分类器"""
        classifier_type = config.get('type')
        raw_params = config.get('params', {})

        # 过滤掉注释字段
        params = {k: v for k, v in raw_params.items() if not k.startswith('_comment')}

        try:
            if classifier_type == 'SVC':
                from sklearn.svm import SVC
                return SVC(**params)
            elif classifier_type == 'RandomForestClassifier':
                from sklearn.ensemble import RandomForestClassifier
                return RandomForestClassifier(**params)
            elif classifier_type == 'LogisticRegression':
                from sklearn.linear_model import LogisticRegression
                return LogisticRegression(**params)
            elif classifier_type == 'MeanField':
                return self._create_meanfield_from_config(config)
            else:
                raise ValueError(f"不支持的分类器类型: {classifier_type}")
        except Exception as e:
            self.logger.error(f"创建分类器失败: {e}")
            # 返回备选分类器
            if 'fallback' in config:
                return self._create_classifier_from_config(config['fallback'])
            else:
                raise

    def _create_meanfield_from_config(self, config):
        """创建MeanField分类器"""
        raw_params = config.get('params', {})
        # 过滤掉注释字段
        params = {k: v for k, v in raw_params.items() if not k.startswith('_comment')}
        try:
            from pyriemann.classification import MeanField
            return MeanField(**params)
        except ImportError:
            self.logger.warning("pyriemann未安装，使用备选分类器")
            if 'fallback' in config:
                return self._create_classifier_from_config(config['fallback'])
            else:
                # 默认备选
                from sklearn.svm import SVC
                return SVC(C=0.1, kernel='rbf', probability=True, random_state=42)

    def _create_default_classifier(self, classifier_name):
        """创建默认分类器（向后兼容）"""
        if classifier_name == 'fbcsp_svm':
            return self._create_optimized_svm_classifier()
        elif classifier_name == 'tef_rf':
            return self._create_optimized_rf_classifier()
        elif classifier_name == 'riemannian_meanfield':
            return self._create_optimized_meanfield_classifier()
        elif classifier_name == 'tangent_space_lr':
            return self._create_optimized_logistic_regression_classifier()
        else:
            raise ValueError(f"未知的分类器名称: {classifier_name}")

    def _create_optimized_svm_classifier(self):
        """创建优化的SVM分类器"""
        try:
            return SVC(
                C=0.1,                    # 降低正则化参数，减少过拟合
                kernel='rbf',             # RBF核
                gamma='scale',
                probability=True,
                random_state=42
            )
        except Exception as e:
            logger.warning(f"优化SVM分类器创建失败: {e}，使用默认配置")
            return SVC(probability=True, random_state=42)

    def _create_optimized_rf_classifier(self):
        """创建优化的随机森林分类器（进一步减少过拟合）"""
        try:
            return RandomForestClassifier(
                n_estimators=30,          # 进一步减少树的数量
                max_depth=3,              # 进一步限制树的深度
                min_samples_split=15,     # 进一步增加分裂所需最小样本数
                min_samples_leaf=8,       # 进一步增加叶节点最小样本数
                max_features=0.5,         # 限制特征选择比例
                bootstrap=True,
                oob_score=True,          # 使用袋外评分
                random_state=42
            )
        except Exception as e:
            logger.warning(f"优化随机森林分类器创建失败: {e}，使用默认配置")
            return RandomForestClassifier(n_estimators=100, random_state=42)

    def _create_optimized_meanfield_classifier(self):
        """创建优化的MeanField分类器（简化模型）"""
        try:
            from pyriemann.classification import MeanField
            # 简化功率列表，减少过拟合
            power_list = [-1, -0.5, 0, 0.5, 1]
            return MeanField(
                power_list=power_list,
                method_label='sum_means',
                metric='riemann',
                n_jobs=1  # 单线程，避免并发问题
            )
        except ImportError:
            logger.warning("pyriemann未安装或版本过低，使用优化SVM替代MeanField")
            return self._create_optimized_svm_classifier()
        except Exception as e:
            logger.warning(f"优化MeanField分类器创建失败: {e}，使用优化SVM替代")
            return self._create_optimized_svm_classifier()

    def _create_optimized_logistic_regression_classifier(self):
        """创建优化的逻辑回归分类器（修正过度正则化）"""
        try:
            return LogisticRegression(
                C=1.0,                    # 恢复默认正则化强度
                max_iter=2000,           # 保持增加的迭代次数
                random_state=42,
                solver='lbfgs',          # 恢复默认求解器
                penalty='l2'             # 使用L2正则化
            )
        except Exception as e:
            logger.warning(f"优化逻辑回归分类器创建失败: {e}，使用默认配置")
            return LogisticRegression(C=1.0, max_iter=1000, random_state=42, solver='lbfgs')

    def _create_meanfield_classifier(self):
        """创建MeanField分类器（基于109位受试者验证的最优配置）"""
        try:
            from pyriemann.classification import MeanField
            # 使用经过大规模验证的最优功率均值列表
            power_list = [-1, -0.75, -0.5, -0.25, -0.1, 0, 0.1, 0.25, 0.5, 0.75, 1]
            return MeanField(power_list=power_list, method_label='sum_means', metric='riemann')
        except ImportError:
            logger.warning("pyriemann未安装或版本过低，使用SVM替代MeanField")
            return SVC(probability=True, random_state=42)
        except Exception as e:
            logger.warning(f"MeanField分类器创建失败: {e}，使用SVM替代")
            return SVC(probability=True, random_state=42)

    def _create_logistic_regression_classifier(self):
        """创建逻辑回归分类器（基于权威论文的标准配置）"""
        try:
            # 使用权威论文中的标准参数配置
            return LogisticRegression(
                C=1.0,                    # 正则化强度
                max_iter=1000,           # 最大迭代次数
                random_state=42,         # 随机种子
                solver='lbfgs'           # 优化算法，适合小数据集
            )
        except Exception as e:
            logger.warning(f"逻辑回归分类器创建失败: {e}，使用SVM替代")
            return SVC(probability=True, random_state=42)
    
    # 旧的train_voting_system方法已删除 - 系统仅支持高性能Stacking训练
    # 请使用train_weighted_voting_system方法

    def _clean_features(self, features: np.ndarray, feature_type: str) -> np.ndarray:
        """清理特征数据，处理无穷值和NaN"""
        try:
            # 检查并处理无穷值和NaN
            if np.any(np.isinf(features)) or np.any(np.isnan(features)):
                logger.warning(f"{feature_type}特征包含无穷值或NaN，进行清理")

                # 替换无穷值和NaN为0
                features = np.nan_to_num(features, nan=0.0, posinf=0.0, neginf=0.0)

                # 如果所有特征都是0，用小的随机值替代
                if np.all(features == 0):
                    logger.warning(f"{feature_type}特征全为0，使用随机值替代")
                    features = np.random.normal(0, 0.01, features.shape)

            # 检查特征范围，避免过大的值
            max_val = np.max(np.abs(features))
            if max_val > 1e6:
                logger.warning(f"{feature_type}特征值过大({max_val})，进行缩放")
                features = features / (max_val / 1000)  # 缩放到合理范围

            return features.astype(np.float32)

        except Exception as e:
            logger.error(f"清理{feature_type}特征失败: {e}")
            # 返回原始特征
            return features

    def _get_covariance_matrices(self, X: np.ndarray) -> np.ndarray:
        """获取协方差矩阵用于MDM分类器"""
        try:
            from pyriemann.estimation import Covariances

            # 使用pyriemann计算协方差矩阵（统一与在线推理一致：OAS）
            cov = Covariances(estimator='oas')
            cov_matrices = cov.fit_transform(X)

            return cov_matrices

        except ImportError:
            logger.warning("pyriemann未安装，无法计算协方差矩阵")
            # 手动计算协方差矩阵
            n_trials, n_channels, n_samples = X.shape
            cov_matrices = np.zeros((n_trials, n_channels, n_channels))

            for i in range(n_trials):
                cov_matrices[i] = np.cov(X[i])

            return cov_matrices

        except Exception as e:
            logger.error(f"计算协方差矩阵失败: {e}")
            # 返回单位矩阵作为备选
            n_trials, n_channels, _ = X.shape
            return np.tile(np.eye(n_channels), (n_trials, 1, 1))

    def _evaluate_classifier(self, classifier, features: np.ndarray, y: np.ndarray) -> List[float]:
        """评估分类器性能"""
        try:
            # 如果样本数太少，减少交叉验证折数
            n_samples = len(y)
            if n_samples < 10:
                return [0.5]  # 样本太少，返回默认分数
            
            cv_folds = min(5, n_samples // 4)  # 确保每折至少有4个样本
            cv_folds = max(2, cv_folds)  # 至少2折
            
            cv = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)
            scores = cross_val_score(classifier, features, y, cv=cv, scoring='accuracy')
            return scores.tolist()
            
        except Exception as e:
            logger.warning(f"交叉验证失败: {e}")
            return [0.5]
    
    def _save_voting_system(self, voting_system,
                           patient_id: str, version_suffix: str) -> str:
        """保存投票系统"""
        filename = f"{patient_id}_{version_suffix}_voting_system.pkl"
        filepath = os.path.join(self.models_dir, filename)
        
        with open(filepath, 'wb') as f:
            pickle.dump(voting_system, f)
        
        # 创建最新版本链接
        latest_filename = f"{patient_id}_latest_voting_system.pkl"
        latest_filepath = os.path.join(self.models_dir, latest_filename)
        
        try:
            if os.path.exists(latest_filepath):
                os.remove(latest_filepath)
            # Windows下创建副本而不是符号链接
            with open(filepath, 'rb') as src, open(latest_filepath, 'wb') as dst:
                dst.write(src.read())
        except Exception as e:
            logger.warning(f"创建最新版本链接失败: {e}")
        
        return filepath
    
    def _save_performance_report(self, voting_system,
                                patient_id: str, version_suffix: str) -> str:
        """保存性能报告"""
        filename = f"{patient_id}_{version_suffix}_performance.json"
        filepath = os.path.join(self.models_dir, filename)
        
        report = {
            'patient_id': patient_id,
            'version': version_suffix,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'classifiers': {}
        }
        
        for name, performance in voting_system.performance.items():
            report['classifiers'][name] = {
                'accuracy': performance.accuracy,
                'cv_mean': performance.cv_mean,
                'cv_std': performance.cv_std,
                'training_time': performance.training_time,
                'n_samples': performance.n_samples
            }
        
        import json
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        return filepath
    
    def load_voting_system(self, patient_id: str, version: str = 'latest'):
        """加载投票系统（优先加载加权投票系统）"""
        # 首先尝试加载加权投票系统
        if version == 'latest':
            weighted_filename = f"weighted_voting_system_{patient_id}_latest.pkl"
            legacy_filename = f"{patient_id}_latest_voting_system.pkl"
        else:
            weighted_filename = f"weighted_voting_system_{patient_id}_{version}.pkl"
            legacy_filename = f"{patient_id}_{version}_voting_system.pkl"

        # 尝试加载加权投票系统
        weighted_filepath = os.path.join(self.models_dir, weighted_filename)
        if os.path.exists(weighted_filepath):
            try:
                with open(weighted_filepath, 'rb') as f:
                    voting_manager = pickle.load(f)
                logger.info(f"加权投票系统加载成功: {weighted_filepath}")
                return voting_manager
            except Exception as e:
                logger.warning(f"加载加权投票系统失败: {e}")

        # 回退到旧的投票系统（向后兼容）
        legacy_filepath = os.path.join(self.models_dir, legacy_filename)
        if os.path.exists(legacy_filepath):
            try:
                with open(legacy_filepath, 'rb') as f:
                    voting_system = pickle.load(f)
                logger.info(f"旧版投票系统加载成功: {legacy_filepath}")
                return voting_system
            except Exception as e:
                logger.error(f"加载旧版投票系统失败: {e}")

        logger.warning(f"未找到投票系统文件: {weighted_filename} 或 {legacy_filename}")
        return None

    def _is_riemannian_classifier(self, classifier) -> bool:
        """判断是否为Riemannian分类器"""
        classifier_type = str(type(classifier))
        return any(riem_type in classifier_type for riem_type in ['MeanField', 'TSClassifier'])

    def train_weighted_voting_system(self, X: np.ndarray, y: np.ndarray,
                                   feature_extractors: Dict[str, Any],
                                   patient_id: str, version_suffix: str) -> Dict[str, str]:
        """
        训练加权投票分类器系统

        Args:
            X: 原始EEG数据 [n_trials, n_channels, n_samples]
            y: 标签 [n_trials]
            feature_extractors: 特征提取器字典
            patient_id: 患者ID
            version_suffix: 版本后缀

        Returns:
            保存的分类器文件路径字典
        """
        logger.info(f"开始训练加权投票分类器系统，样本数: {X.shape[0]}")

        # 导入加权投票管理器
        from services.weighted_voting_classifier import WeightedVotingManager

        # 创建加权投票管理器
        voting_manager = WeightedVotingManager()

        # 准备分类器数据
        classifiers_data = {}
        saved_files = {}

        for name, config in self.classifier_configs.items():
            try:
                feature_type = config['feature_type']
                classifier = config['classifier']

                # 获取对应的特征提取器
                if feature_type not in feature_extractors:
                    logger.warning(f"未找到{feature_type}特征提取器，跳过{name}")
                    continue

                feature_extractor = feature_extractors[feature_type]

                # 提取特征
                logger.info(f"使用{feature_type}特征提取器提取特征...")
                features = feature_extractor.transform(X)
                logger.info(f"{feature_type}特征形状: {features.shape}")

                # 特殊处理Riemannian特征（需要协方差矩阵）
                if feature_type == 'riemannian' and self._is_riemannian_classifier(classifier):
                    # 对于Riemannian分类器（MDM、MeanField等），需要协方差矩阵而不是向量化特征
                    features = self._get_covariance_matrices(X)
                    logger.info(f"Riemannian协方差矩阵形状: {features.shape}")
                else:
                    # 清理特征数据
                    features = self._clean_features(features, feature_type)

                # 先进行交叉验证评估（避免数据泄露）
                cv_scores = self._evaluate_classifier(classifier, features, y)

                # 然后在全数据集上训练分类器
                start_time = time.time()
                classifier.fit(features, y)
                training_time = time.time() - start_time

                # 创建性能记录
                performance = ClassifierPerformance(
                    accuracy=accuracy_score(y, classifier.predict(features)),
                    cv_scores=cv_scores,
                    cv_mean=np.mean(cv_scores),
                    cv_std=np.std(cv_scores),
                    training_time=training_time,
                    n_samples=X.shape[0]
                )

                # 保存分类器数据
                classifiers_data[name] = {
                    'classifier': classifier,
                    'feature_extractor': feature_extractor,
                    'performance': performance
                }

                logger.info(f"{name}训练完成 - 准确率: {performance.accuracy:.3f}, "
                           f"交叉验证: {performance.cv_mean:.3f}±{performance.cv_std:.3f}")

            except Exception as e:
                logger.error(f"{name}训练失败: {e}")
                continue

        if not classifiers_data:
            logger.warning("没有成功训练任何分类器")
            return saved_files

        # 训练加权投票系统
        try:
            voting_manager.fit(X, y, classifiers_data)

            # 🔧 初始化动态权重管理器（如果配置启用）
            try:
                dynamic_config = self.classifier_configs.get('dynamic_weights', {})
                if dynamic_config.get('enabled', False):
                    voting_manager.initialize_dynamic_weights(dynamic_config)
                    logger.info("动态权重管理器已初始化")
            except Exception as e:
                logger.warning(f"动态权重管理器初始化失败: {e}")

            # 🔧 初始化分类器平滑器（如果配置启用）
            try:
                smoothing_config = self.classifier_configs.get('classifier_smoothing', {})
                if smoothing_config.get('enabled', False):
                    # 提取分类器特定的配置
                    classifier_configs = {k: v for k, v in smoothing_config.items()
                                        if k not in ['enabled', '_comment']}
                    voting_manager.initialize_classifier_smoothing(classifier_configs)
                    logger.info("分类器平滑器已初始化")
            except Exception as e:
                logger.warning(f"分类器平滑器初始化失败: {e}")

            # 保存加权投票系统
            save_path = self._save_weighted_voting_system(voting_manager, patient_id, version_suffix)
            saved_files['weighted_voting_system'] = save_path

            # 保存性能报告
            report_path = self._save_weighted_voting_report(voting_manager, patient_id, version_suffix)
            saved_files['performance_report'] = report_path

            logger.info(f"加权投票分类器系统训练完成，保存到: {save_path}")

        except Exception as e:
            logger.error(f"加权投票系统训练失败: {e}")

        return saved_files

    def _save_weighted_voting_system(self, voting_manager, patient_id: str, version_suffix: str) -> str:
        """保存加权投票系统"""
        try:
            filename = f"weighted_voting_system_{patient_id}_{version_suffix}.pkl"
            save_path = os.path.join(self.models_dir, filename)

            with open(save_path, 'wb') as f:
                pickle.dump(voting_manager, f)

            logger.info(f"加权投票系统已保存到: {save_path}")

            # 创建最新版本副本（latest）
            latest_filename = f"weighted_voting_system_{patient_id}_latest.pkl"
            latest_filepath = os.path.join(self.models_dir, latest_filename)

            try:
                if os.path.exists(latest_filepath):
                    os.remove(latest_filepath)
                # Windows下创建副本而不是符号链接
                with open(save_path, 'rb') as src, open(latest_filepath, 'wb') as dst:
                    dst.write(src.read())
                logger.info(f"最新版本副本已创建: {latest_filepath}")
            except Exception as e:
                logger.warning(f"创建最新版本副本失败: {e}")

            # 返回相对于数据目录的相对路径
            from utils.path_manager import get_data_dir_path
            data_dir = get_data_dir_path()
            try:
                relative_path = os.path.relpath(save_path, data_dir)
                return relative_path
            except ValueError:
                # 如果无法计算相对路径，使用绝对路径
                return save_path

        except Exception as e:
            logger.error(f"保存加权投票系统失败: {e}")
            return None

    def _save_weighted_voting_report(self, voting_manager, patient_id: str, version_suffix: str) -> str:
        """保存加权投票系统性能报告"""
        try:
            # 获取系统状态
            status = voting_manager.get_system_status()

            # 生成报告
            report_lines = []
            report_lines.append("=== 加权投票分类器系统性能报告 ===")
            report_lines.append(f"患者ID: {patient_id}")
            report_lines.append(f"版本: {version_suffix}")
            report_lines.append(f"训练时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
            report_lines.append("")

            # 系统配置
            report_lines.append("=== 系统配置 ===")
            report_lines.append(f"分类器数量: {status['n_classifiers']}")
            report_lines.append(f"权重方法: {status['weight_method']}")
            report_lines.append(f"难度等级: {status['difficulty_level']['level']} - {status['difficulty_level']['name']}")
            report_lines.append(f"触发阈值: {status['thresholds']['trigger']:.3f}")
            report_lines.append("")

            # 分类器性能
            report_lines.append("=== 分类器性能 ===")
            for name, perf in status['performance_summary'].items():
                report_lines.append(f"{name}:")
                report_lines.append(f"  准确率: {perf['accuracy']:.3f}")
                report_lines.append(f"  交叉验证: {perf['cv_mean']:.3f}±{perf['cv_std']:.3f}")
                report_lines.append("")

            # 投票权重
            report_lines.append("=== 投票权重 ===")
            for name, weight in status['voting_weights'].items():
                report_lines.append(f"{name}: {weight:.3f}")

            # 保存报告
            filename = f"weighted_voting_report_{patient_id}_{version_suffix}.txt"
            save_path = os.path.join(self.models_dir, filename)

            with open(save_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(report_lines))

            logger.info(f"加权投票性能报告已保存到: {save_path}")

            # 创建最新版本副本（latest）
            latest_filename = f"weighted_voting_report_{patient_id}_latest.txt"
            latest_filepath = os.path.join(self.models_dir, latest_filename)

            try:
                if os.path.exists(latest_filepath):
                    os.remove(latest_filepath)
                # 创建报告副本
                with open(save_path, 'r', encoding='utf-8') as src, open(latest_filepath, 'w', encoding='utf-8') as dst:
                    dst.write(src.read())
                logger.info(f"最新版本报告副本已创建: {latest_filepath}")
            except Exception as e:
                logger.warning(f"创建最新版本报告副本失败: {e}")

            # 返回相对于数据目录的相对路径
            from utils.path_manager import get_data_dir_path
            data_dir = get_data_dir_path()
            try:
                relative_path = os.path.relpath(save_path, data_dir)
                return relative_path
            except ValueError:
                # 如果无法计算相对路径，使用绝对路径
                return save_path

        except Exception as e:
            logger.error(f"保存加权投票性能报告失败: {e}")
            return None
